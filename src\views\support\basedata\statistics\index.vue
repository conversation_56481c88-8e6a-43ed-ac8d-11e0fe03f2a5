<template>
  <section>
    <el-row :span="24">
      <el-col :span="5">
        <basic-container style="padding-right: 0">
          <avue-tabs :option="leftTabsOption"></avue-tabs>
          <section class="left-container">
            <div class="red pb-1">注：部件数据量过大，建议选择小类查询，否则会导致查询缓慢或页面崩溃！</div>
            <ty-tree
              :data="whTree"
              :show-btns="false"
              :show-checkbox="false"
              :show-loading="treeLoading"
              :show-title="false"
              :default-expand-all="true"
              :tree-props="whTreeProps"
              @node-click="handleNodeClick"
              @refresh="handleLoadWhTree"
              ref="whTreeREF"
            >
              <template
                slot="icon"
                slot-scope="{ node, data }"
                v-if="data.nodeLevel === '4' && data.iconUrl !== undefined && data.iconUrl !== '' && data.iconUrl !== null"
              >
                <img :src="`${ossFileUrl}${data.iconUrl}`" style="width: 16px; height: 16px" />
              </template>
            </ty-tree>
          </section>
        </basic-container>
      </el-col>
      <el-col :span="19">
        <basic-container>
          <avue-tabs :option="rightTabsOption"></avue-tabs>
          <section class="right-container">
            <avue-crud
              :data="tableData"
              :option="statisticsOption"
              :page="page"
              :table-loading="tableLoading"
              :span-method="objectSpanMethod"
              @current-change="handleCurrentChange"
              @refresh-change="handleLoadAndQuery"
              @selection-change="handleSelectionChange"
              @size-change="handleSizeChange"
              @search-reset="handleSearchReset"
              @sort-change="handleSortChange"
              ref="statRef"
              v-model="statisticsForm"
            >
              <template slot="searchRight">
                <!-- 区域 -->
                <el-form-item label="所属区域">
                  <ty-area-select :default-expand-all="true" :search="true" :need-all="false" ref="areaSelectRef" v-model="areaCode"></ty-area-select>
                </el-form-item>
                <el-form-item>
                  <el-button @click="handleStatistics" type="primary" size="medium">
                    {{ SystemPrompt.Button.statistics }}
                  </el-button>
                </el-form-item>
              </template>
            </avue-crud>
          </section>
        </basic-container>
      </el-col>
    </el-row>
  </section>
</template>

<script>
import caseClassApi from '@supportApi/caseClass'
import { statisticsOption } from '@supportConst/basedata/statistics/statistics'
import { MethodsMixin, QueryParamsMixin } from '@/mixins/global'
import store from '@/store'
const access_token = store.getters['topevery/token/access_token']
export default {
  name: 'support-basedata-statistics',
  mixins: [MethodsMixin, QueryParamsMixin],
  data() {
    return {
      myToken: {
        Authorization: 'Bearer ' + access_token
      },
      statisticsOption: statisticsOption,
      tableLoading: false,
      // 部件树
      treeLoading: false,
      currentNode: null,
      whTree: [],
      whTreeProps: {
        id: 'id',
        children: 'children',
        label: 'nodeName'
      },
      leftTabsOption: {
        column: [
          {
            icon: 'el-icon-info',
            label: '部件树',
            prop: 'tab'
          }
        ]
      },
      // right
      statisticsForm: {},
      rightTabsOption: {
        column: [
          {
            icon: 'el-icon-info',
            label: '空间统计',
            prop: 'tab'
          }
        ]
      },
      areaCode: '-1',
      dataStore: [],
      dataStoreTransform: [],
      tableData: [],
      listQuery: {
        typeList: [],
        page: true,
        params: [],
        ascs: [],
        descs: []
      }
    }
  },
  watch: {
    areaCode: {
      handler() {
        this.handleLoadAndQuery()
      },
      deep: true
    },
    dataStore: {
      handler() {
        const dataStoreTransform = []
        this.dataStore.forEach(item => {
          var it = {}
          it.bigCaseClassCode = item.bigCaseClassCode
          it.bigCaseClassName = item.bigCaseClassName
          it.smallCaseClassCode = item.smallCaseClassCode
          it.smallCaseClassName = item.smallCaseClassName

          if (item.whStateCountVOList !== undefined && item.whStateCountVOList.length > 0) {
            item.whStateCountVOList.forEach(itemVo => {
              dataStoreTransform.push({ ...it, ...itemVo })
            })
          } else {
            it.state = '-'
            it.count = '-'
            dataStoreTransform.push(it)
          }
        })
        this.tableData = dataStoreTransform
      }
    }
  },
  created() {
    this.$nextTick(() => {
      this.handleLoadWhTree()
      this.rightTabs = this.rightTabsOption.column[0]
    })
  },
  methods: {
    /**
     *
     * @author: <EMAIL>
     * @description: 获取部门树
     */
    handleLoadWhTree() {
      this.treeLoading = true
      caseClassApi
        .getWhTree({
          industryType: '01'
        })
        .then(ret => {
          this.treeLoading = false
          this.whTree = ret.data

          setTimeout(() => {
            this.$refs.whTreeREF.filterNodeText()
          })
        })
        .catch(() => {
          this.treeLoading = false
        })
    },
    /**
     * @author: <EMAIL>
     * @description: 树节点点击事件
     */
    handleNodeClick(data) {
      this.page.current = 1
      if (data.nodeLevel === '2') {
        this.currentNode = data
        this.listQuery.typeList = []
        this.handleLoadAndQuery()
      } else if (data.nodeLevel === '3') {
        this.currentNode = data
        this.listQuery.typeList = []
        data.children.forEach(item => {
          this.listQuery.typeList.push(item.whType)
        })
        this.handleLoadAndQuery()
      } else {
        this.currentNode = data
        this.listQuery.typeList = [this.currentNode.whType]
        this.handleLoadAndQuery()
      }
    },
    /**
     * @description: 统计
     * @author: <EMAIL>
     */
    handleStatistics() {
      this.handleLoadAndQuery()
    },
    /**
     * @description: 获取列表数据
     * @author: <EMAIL>
     */
    handleLoadAndQuery() {
      this.tableLoading = true
      this.areaCode === undefined ? '-1' : this.areaCode
      this.listQuery.areaCode = this.areaCode
      const { current, limit } = this.page
      const listQuery = Object.assign({}, { current, limit }, this.listQuery)
      caseClassApi
        .countWhByTypeAndArea(listQuery)
        .then(ret => {
          this.tableLoading = false
          this.dataStore = ret.data
          this.page.total = ret.total
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    handleSizeChange(val) {
      this.page.limit = val
      this.handleLoadAndQuery()
    },
    handleCurrentChange(val) {
      this.page.current = val
      this.handleLoadAndQuery()
    },
    /**
     * @author: <EMAIL>
     * @Date: 2019-12-04 10:03:08
     * @description: 清空
     */
    handleSearchReset() {
      this.areaCode = '-1'
      this.handleLoadAndQuery()
    },
    /**
     * @description: 排序方法
     * @author: <EMAIL>
     */
    handleSortChange(event) {
      this.listQuery.descs = []
      this.listQuery.ascs = []

      if (event.order.indexOf('descending') !== -1) {
        this.listQuery.descs.push(event.prop)
      } else if (event.order.indexOf('ascending') !== -1) {
        this.listQuery.ascs.push(event.prop)
      }

      this.handleLoadAndQuery()
    },
    /**
     * @description: 合并单元格
     * @author: <EMAIL>
     */
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (this.tableData.length === 0) {
        return { rowspan: 0, colspan: 0 }
      }
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          const rowspan = this.tableData.filter(item => {
            return item.bigCaseClassCode === row.bigCaseClassCode
          }).length
          return { rowspan: rowspan, colspan: 1 }
        } else {
          if (this.tableData[rowIndex - 1].bigCaseClassCode === row.bigCaseClassCode) {
            return { rowspan: 0, colspan: 0 }
          } else {
            const rowspan = this.tableData.filter(item => {
              return item.bigCaseClassCode === row.bigCaseClassCode
            }).length
            return { rowspan: rowspan, colspan: 1 }
          }
        }
      }
      if (columnIndex === 1) {
        if (rowIndex === 0) {
          const rowspan = this.tableData.filter(item => {
            return item.bigCaseClassName === row.bigCaseClassName && item.bigCaseClassCode === row.bigCaseClassCode
          }).length
          return { rowspan: rowspan, colspan: 1 }
        } else {
          if (
            this.tableData[rowIndex - 1].bigCaseClassName === row.bigCaseClassName &&
            this.tableData[rowIndex - 1].bigCaseClassCode === row.bigCaseClassCode
          ) {
            return { rowspan: 0, colspan: 0 }
          } else {
            const rowspan = this.tableData.filter(item => {
              return item.bigCaseClassName === row.bigCaseClassName && item.bigCaseClassCode === row.bigCaseClassCode
            }).length
            return { rowspan: rowspan, colspan: 1 }
          }
        }
      }
      if (columnIndex === 2) {
        if (rowIndex === 0) {
          const rowspan = this.tableData.filter(item => {
            return item.smallCaseClassCode === row.smallCaseClassCode && item.bigCaseClassCode === row.bigCaseClassCode
          }).length
          return { rowspan: rowspan, colspan: 1 }
        } else {
          if (
            this.tableData[rowIndex - 1].smallCaseClassCode === row.smallCaseClassCode &&
            this.tableData[rowIndex - 1].bigCaseClassCode === row.bigCaseClassCode
          ) {
            return { rowspan: 0, colspan: 0 }
          } else {
            const rowspan = this.tableData.filter(item => {
              return item.smallCaseClassCode === row.smallCaseClassCode && item.bigCaseClassCode === row.bigCaseClassCode
            }).length
            return { rowspan: rowspan, colspan: 1 }
          }
        }
      }
      if (columnIndex === 3) {
        if (rowIndex === 0) {
          const rowspan = this.tableData.filter(item => {
            return item.smallCaseClassName === row.smallCaseClassName && item.bigCaseClassCode === row.bigCaseClassCode
          }).length
          return { rowspan: rowspan, colspan: 1 }
        } else {
          if (
            this.tableData[rowIndex - 1].smallCaseClassName === row.smallCaseClassName &&
            this.tableData[rowIndex - 1].bigCaseClassCode === row.bigCaseClassCode
          ) {
            return { rowspan: 0, colspan: 0 }
          } else {
            const rowspan = this.tableData.filter(item => {
              return item.smallCaseClassName === row.smallCaseClassName && item.bigCaseClassCode === row.bigCaseClassCode
            }).length
            return { rowspan: rowspan, colspan: 1 }
          }
        }
      }
    }
  },
  computed: {
    industryType() {
      return this.$store.getters['topevery/dictionary/commonDics']('industry_type')
    },
    fileUrl() {
      return this.$store.getters['topevery/systemConfig/fileUrl']
    },
    ossFileUrl() {
      return this.$store.getters['topevery/systemConfig/ossFileUrl']
    }
  }
}
</script>

<style lang="scss" scoped>
.left-container {
  :global(.el-tree) {
    height: calc(100vh - 284px);
    overflow-y: auto;
  }
  .red {
    color: #ff0000;
  }
  .pb-1 {
    padding-bottom: 10px;
  }
}
.right-container {
  :global(.el-dialog__body .avue-form) {
    padding: 0px 10px;

    :global(.el-form-item) {
      margin-right: 10px;
    }
  }
}
</style>
